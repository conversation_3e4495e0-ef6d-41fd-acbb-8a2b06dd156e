"""
Gateway Authentication Module

This module handles authentication for Cortexa services when running behind
an Apache APISIX gateway with JWT authentication.

The gateway validates JWT tokens and forwards user identity information via headers.
This module parses and validates that information to establish user context.
"""

import json
import base64
import logging
from typing import Any
from datetime import datetime, timezone
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class GatewayAuthenticationError(Exception):
    """Exception raised for authentication errors."""
    pass


@dataclass
class AuthenticatedUser:
    """
    Represents an authenticated user with basic identity information.

    This data is extracted from headers provided by the APISIX gateway
    after successful JWT validation.
    """

    # Core identity
    user_id: str
    email: str

    # Metadata
    user_metadata: dict[str, Any]
    app_metadata: dict[str, Any]

    # Token information
    token_kid: str
    token_issuer: str
    token_audience: str
    token_expires_at: int
    token_issued_at: int

    # Validation timestamp
    validated_at: str

    def __post_init__(self):
        """Validate user data after initialization."""
        if not self.user_id:
            raise ValueError("user_id is required")
        if not self.email:
            raise ValueError("email is required")

    @property
    def is_authenticated(self) -> bool:
        """Check if user is authenticated (always True for this class)."""
        return True

    @property
    def is_token_expired(self) -> bool:
        """Check if the JWT token has expired."""
        current_timestamp = int(datetime.now(timezone.utc).timestamp())
        return current_timestamp >= self.token_expires_at

    def to_dict(self) -> dict[str, Any]:
        """Convert user to dictionary for serialization."""
        return {
            'user_id': self.user_id,
            'email': self.email,
            'user_metadata': self.user_metadata,
            'app_metadata': self.app_metadata,
            'token': {
                'kid': self.token_kid,
                'issuer': self.token_issuer,
                'audience': self.token_audience,
                'expires_at': self.token_expires_at,
                'issued_at': self.token_issued_at
            },
            'validated_at': self.validated_at
        }


class GatewayAuthParser:
    """
    Parses authentication information from gateway headers.
    
    The APISIX gateway validates JWT tokens and forwards user context
    via standardized headers. This class extracts and validates that information.
    """
    
    # Header names used by the JWT authorizer plugin
    USER_CONTEXT_HEADER = "X-User-Context"
    USER_ID_HEADER = "X-User-ID"
    USER_EMAIL_HEADER = "X-User-Email"
    
    def __init__(self):
        """Initialize the gateway auth parser."""
        self.logger = logging.getLogger(f"{__name__}.GatewayAuthParser")
    
    def parse_user_from_headers(self, headers: dict[str, str]) -> AuthenticatedUser | None:
        """
        Parse authenticated user information from gateway headers.
        
        Args:
            headers: HTTP headers from the request
            
        Returns:
            AuthenticatedUser instance if valid headers found, None otherwise
            
        Raises:
            HTTPException: If headers are present but invalid
        """
        try:
            # Check if user context header is present
            user_context_b64 = headers.get(self.USER_CONTEXT_HEADER)
            if not user_context_b64:
                self.logger.debug("No user context header found - request not authenticated")
                return None
            
            # Decode base64 user context
            try:
                user_context_json = base64.b64decode(user_context_b64).decode('utf-8')
                user_context = json.loads(user_context_json)
            except (ValueError, json.JSONDecodeError) as e:
                self.logger.error(f"Failed to decode user context header: {e}")
                raise GatewayAuthenticationError("Invalid user context header format")
            
            # Extract user information
            user_info = user_context.get('user', {})
            token_info = user_context.get('token', {})

            # Create authenticated user
            authenticated_user = AuthenticatedUser(
                user_id=user_info.get('id', ''),
                email=user_info.get('email', ''),
                user_metadata=user_info.get('metadata', {}),
                app_metadata=user_info.get('app_metadata', {}),
                token_kid=token_info.get('kid', ''),
                token_issuer=token_info.get('iss', ''),
                token_audience=token_info.get('aud', ''),
                token_expires_at=token_info.get('exp', 0),
                token_issued_at=token_info.get('iat', 0),
                validated_at=user_context.get('validated_at', '')
            )
            
            # Validate token expiration
            if authenticated_user.is_token_expired:
                self.logger.warning(f"Token expired for user {authenticated_user.user_id}")
                raise GatewayAuthenticationError("Token has expired")
            
            self.logger.info(f"Successfully parsed user context for {authenticated_user.user_id}")
            return authenticated_user

        except Exception as e:
            self.logger.error(f"Unexpected error parsing user context: {e}")
            raise GatewayAuthenticationError("Failed to parse user authentication")
