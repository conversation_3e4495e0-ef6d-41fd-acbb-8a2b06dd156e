import asyncio
from contextlib import asynccontextmanager
from typing import As<PERSON><PERSON>enerator

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from cortexacommon.monitoring import setup_monitoring
from cortexacommon.logging import get_logger, setup_service_logging

from src.api.v1.router import api_router
from src.core.config import settings
from src.core.tracing import init_tracing
from src.core.metrics import setup_metrics
from src.core.context import get_app_context

# Setup common logging/monitoring
setup_service_logging(service_name=settings.service_name, level="INFO")
setup_monitoring(service_name=settings.service_name, app=None)
logger = get_logger(__name__)

# Background task for connection cleanup
cleanup_task: asyncio.Task | None = None


async def cleanup_idle_connections():
    """Background task to clean up idle connections."""
    while True:
        try:
            # Get the application context and connection state manager
            app_context = await get_app_context()
            if not app_context.is_initialized:
                await asyncio.sleep(60)
                continue

            # Clean up connections idle for more than the configured timeout
            idle_call_ids = await app_context.connection_state_manager.cleanup_idle_connections(
                timeout=settings.ws_connection_timeout
            )

            if idle_call_ids:
                logger.info(f"Cleaned up {len(idle_call_ids)} idle connections",
                           call_ids=idle_call_ids)

            # Sleep for 1 minute before next cleanup
            await asyncio.sleep(60)

        except Exception as e:
            logger.error(f"Error in connection cleanup task: {e}")
            await asyncio.sleep(60)  # Continue after error


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager for startup and shutdown events.
    
    This handles:
    - Starting background tasks
    - Initializing resources
    - Cleaning up on shutdown
    """
    # Startup
    logger.info("Starting Voice Gateway service",
                service=settings.service_name,
                port=settings.port)

    # Initialize tracing
    init_tracing()

    # Start background cleanup task
    global cleanup_task
    cleanup_task = asyncio.create_task(cleanup_idle_connections())

    # Initialize application context
    app_context = await get_app_context()
    await app_context.initialize()

    logger.info("Voice Gateway service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Voice Gateway service")

    # Cancel background tasks
    if cleanup_task:
        cleanup_task.cancel()
        try:
            await cleanup_task
        except asyncio.CancelledError:
            pass

    # Clean up application context (this will also clean up active connections)
    app_context = await get_app_context()
    await app_context.cleanup()

    logger.info("Voice Gateway service shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="Cortexa Voice Gateway Service",
    description="Real-time voice translation service for the Cortexa platform",
    version="0.1.0",
    openapi_url="/api/v1/openapi.json",
    docs_url="/api/v1/docs",
    redoc_url="/api/v1/redoc",
    lifespan=lifespan,
)

# Set up metrics
setup_metrics(app)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix="/api/v1")

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
    )
