"""
Application context for managing shared resources in the voice-gateway service.

This module provides a centralized way to manage application-wide resources
like S2ST processor instances, ensuring proper initialization and cleanup.
"""

import asyncio
from opentelemetry import trace
from cortexacommon.logging import get_logger

from .events import EventPublisher
from ..pipeline.s2st import S2STProcessor
from ..pipeline.state import ConnectionStateManager

logger = get_logger(__name__)


class ApplicationContext:
    """
    Application context for managing shared resources.
    
    This class provides a singleton pattern for managing application-wide
    resources like S2ST processor instances.
    """
    
    _instance: 'ApplicationContext' | None = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        """Initialize the application context."""
        self._s2st_processor: S2STProcessor | None = None
        self._events_publisher: EventPublisher | None = None
        self._tracer: trace.Tracer | None = None
        self._connection_state_manager: ConnectionStateManager | None = None

        self._initialized = False
    
    @classmethod
    async def get_instance(cls) -> 'ApplicationContext':
        """
        Get the singleton instance of the application context.
        
        Returns:
            ApplicationContext: The singleton instance
        """
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    @property
    def s2st_processor(self) -> S2STProcessor | None:
        """
        Get the S2ST processor instance.
        
        Returns:
            Optional[S2STProcessor]: The S2ST processor instance if initialized
        """
        return self._s2st_processor
    
    @property
    def events_publisher(self) -> EventPublisher:
        """
        Get the event publisher instance.
        
        Returns:
            Optional[EventPublisher]: The event publisher instance if initialized
        """
        assert self._events_publisher is not None, "Event publisher not initialized"
        return self._events_publisher
    
    @property
    def tracer(self) -> trace.Tracer | None:
        """
        Get the tracer instance.

        Returns:
            Optional[trace.Tracer]: The tracer instance if initialized
        """
        return self._tracer

    @property
    def connection_state_manager(self) -> ConnectionStateManager:
        """
        Get the connection state manager instance.

        Returns:
            ConnectionStateManager: The connection state manager instance if initialized
        """
        assert self._connection_state_manager is not None, "Connection state manager not initialized"
        return self._connection_state_manager
    
    @property
    def is_initialized(self) -> bool:
        """
        Check if the application context is initialized.
        
        Returns:
            bool: True if initialized, False otherwise
        """
        return self._initialized
    
    async def initialize(self) -> None:
        """
        Initialize the application context and its resources.
        
        This method initializes the S2ST processor and other shared resources.
        """
        if self._initialized:
            logger.warning("Application context already initialized")
            return
        
        try:
            logger.info("Initializing application context...")
            
            # Initialize S2ST processor
            logger.info("Creating S2ST processor...")
            self._s2st_processor = S2STProcessor()
            await self._s2st_processor.initialize()
            
            # Initialize event publisher
            logger.info("Creating event publisher...")
            self._events_publisher = EventPublisher()
            await self._events_publisher.initialize()

            # Initialize connection state manager
            logger.info("Creating connection state manager...")
            self._connection_state_manager = ConnectionStateManager()

            # Initialize tracing
            logger.info("Creating tracer...")
            common_setup_tracing(get_monitoring_settings('voice-gateway'))
            
            self._initialized = True
            logger.info("Application context initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize application context: {e}")
            self._initialized = False
            # Don't re-raise - allow service to start even if S2ST fails
            # Individual connections will handle the error appropriately
    
    async def cleanup(self) -> None:
        """
        Clean up the application context and its resources.
        
        This method should be called during application shutdown.
        """
        logger.info("Cleaning up application context...")
        
        try:
            if self._s2st_processor:
                # S2STProcessor doesn't have explicit cleanup method
                # but we can set it to None to allow garbage collection
                self._s2st_processor = None
                logger.info("S2ST processor cleaned up")

            if self._connection_state_manager:
                # Clean up any remaining connections
                active_connections = await self._connection_state_manager.get_all_connections()
                if active_connections:
                    logger.info(f"Cleaning up {len(active_connections)} remaining connections")
                    for state in active_connections:
                        try:
                            await state.cleanup()
                        except Exception as e:
                            logger.error(f"Error cleaning up connection {state.call_id}: {e}")
                self._connection_state_manager = None
                logger.info("Connection state manager cleaned up")

            self._initialized = False
            logger.info("Application context cleanup complete")
            
        except Exception as e:
            logger.error(f"Error during application context cleanup: {e}")
    
    @classmethod
    async def reset_instance(cls) -> None:
        """
        Reset the singleton instance (mainly for testing).
        
        This method should only be used in test scenarios.
        """
        async with cls._lock:
            if cls._instance:
                await cls._instance.cleanup()
            cls._instance = None


# Global function to get the application context
async def get_app_context() -> ApplicationContext:
    """
    Get the application context instance.
    
    Returns:
        ApplicationContext: The application context instance
    """
    return await ApplicationContext.get_instance()
