import asyncio
from cortexacommon.logging import get_logger
from cortexacommon.monitoring import TracingMixin
import time

from .state import ConnectionState
from .providers.factory import ProviderFactory
from .providers.base import BaseVADProvider, BaseSTTProvider, BaseTranslationProvider, BaseTTSProvider
from ..core.config import settings
from ..core.events import get_event_publisher
from ..core.metrics import MetricsCollector

logger = get_logger(__name__)


class S2STProcessor(TracingMixin):
    """Main Speech-to-Speech Translation processor using modular providers."""

    def __init__(self):
        """Initialize S2ST processor with configured providers."""
        super().__init__()
        self.vad: BaseVADProvider | None = None
        self.stt: BaseSTTProvider | None = None
        self.translator: BaseTranslationProvider | None = None
        self.tts: BaseTTSProvider | None = None

        # Processing state
        self.audio_buffer = bytearray()
        self.vad_frame_size_bytes = 0
        self.min_speech_duration = 1.0   # Minimum speech duration in seconds
        self.max_silence_duration = 2.0  # Maximum silence before processing
        self.speech_buffer = bytearray()
        self.last_speech_time = 0.0
        self.is_in_speech = False

        # Initialization status
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize all providers and pre-warm models."""
        try:
            logger.info("Initializing S2ST processor...")

            # Create providers using factory
            logger.info("Creating VAD provider...")
            self.vad = ProviderFactory.create_vad_provider(
                settings.vad_provider,
                aggressiveness=settings.vad_aggressiveness,
                frame_duration_ms=settings.vad_frame_duration_ms
            )

            logger.info("Creating STT provider...")
            self.stt = ProviderFactory.create_stt_provider(
                settings.stt_provider,
                model_size=settings.whisper_model_size,
                compute_type=settings.whisper_compute_type,
                device=settings.whisper_device,
                api_key=settings.deepgram_api_key,
                model=settings.deepgram_model,
                language=settings.deepgram_language
            )

            logger.info("Creating translation provider...")
            self.translator = ProviderFactory.create_translation_provider(
                settings.translation_provider,
                model=settings.translation_model,
                device=settings.translation_device
            )

            logger.info("Creating TTS provider...")
            self.tts = ProviderFactory.create_tts_provider(
                settings.tts_provider,
                api_key=settings.tts_api_key or settings.deepgram_api_key,
                model=settings.tts_model if settings.tts_provider == "openai" else settings.deepgram_tts_model,
                voice=settings.tts_voice
            )

            # Initialize all providers
            logger.info("Initializing VAD provider...")
            await self.vad.initialize()

            logger.info("Initializing STT provider...")
            await self.stt.initialize()

            logger.info("Initializing translation provider...")
            await self.translator.initialize()

            logger.info("Initializing TTS provider...")
            await self.tts.initialize()

            # Set up VAD frame size
            self.vad_frame_size_bytes = self.vad.frame_size * 2  # 2 bytes per sample

            # Pre-warm models with dummy data
            logger.info("Pre-warming models...")
            await self._prewarm_models()

            self._initialized = True
            logger.info("S2ST processor initialized and pre-warmed successfully")

        except Exception as e:
            logger.error(f"Failed to initialize S2ST processor: {e}")
            self._initialized = False
            raise

    @property
    def is_initialized(self) -> bool:
        """Check if processor is fully initialized."""
        return self._initialized

    async def get_health_status(self) -> dict:
        """Get health status of all providers."""
        if not self._initialized:
            return {"status": "not_initialized", "providers": {}}

        return {
            "status": "initialized",
            "providers": {
                "vad": {
                    "provider": self.vad.__class__.__name__,
                    "initialized": self.vad.is_initialized
                },
                "stt": await self.stt.health_check(),
                "translation": await self.translator.health_check(),
                "tts": await self.tts.health_check()
            }
        }

    async def process_audio_chunk(self, state: ConnectionState) -> None:
        """
        Process audio chunks from the connection state.

        This method runs the main processing loop that:
        1. Receives audio chunks from the inbound queue
        2. Buffers audio data for VAD frame processing
        3. Runs VAD on properly-sized frames
        4. Accumulates speech segments
        5. Processes complete speech segments through S2ST pipeline

        Args:
            state: Connection state with audio queues and metadata
        """
        if not self._initialized:
            logger.error(f"S2ST processor not initialized for call {state.call_id}")
            return

        logger.info(f"Starting S2ST processor for call {state.call_id}")

        try:
            while state.state.value in ["active", "processing"]:
                try:
                    # Get audio chunk from inbound queue
                    raw_audio_chunk = await asyncio.wait_for(
                        state.inbound_queue.get(), 
                        timeout=1.0
                    )

                    # Add incoming data to our buffer
                    self.audio_buffer.extend(raw_audio_chunk)

                    # Process all full frames available in the buffer
                    while len(self.audio_buffer) >= self.vad_frame_size_bytes:
                        # Extract one frame for VAD processing
                        vad_frame = self.audio_buffer[:self.vad_frame_size_bytes]
                        
                        # Remove the processed frame from the buffer
                        self.audio_buffer = self.audio_buffer[self.vad_frame_size_bytes:]

                        # Process the correctly-sized frame
                        await self._process_chunk(state, vad_frame)
                    
                    # Mark task as done
                    state.inbound_queue.task_done()
                    
                except asyncio.TimeoutError:
                    # Check for pending speech to process
                    await self._check_pending_speech(state)
                    continue
                    
                except Exception as e:
                    logger.error(f"Error processing audio chunk: {e}")
                    state.add_error("processing_error", str(e))
                    
        except Exception as e:
            logger.error(f"S2ST processor error for call {state.call_id}: {e}")
            state.add_error("s2st_processor_error", str(e))
        
        logger.info(f"S2ST processor ended for call {state.call_id}")

    async def _process_chunk(self, state: ConnectionState, audio_chunk: bytes) -> None:
        """Process a single, correctly-sized VAD audio frame."""
        current_time = time.time()
        
        # This check will now pass, because `audio_chunk` is exactly vad_frame_size_bytes
        is_speech = self.vad.is_speech(audio_chunk)
        
        if is_speech:
            if not self.is_in_speech:
                # Start of speech segment
                self.is_in_speech = True
                self.speech_buffer.clear()
                logger.debug(f"Speech started for call {state.call_id}")
            
            self.speech_buffer.extend(audio_chunk)
            self.last_speech_time = current_time
            state.last_vad_activity = current_time
            
        elif self.is_in_speech:
            # Check if we should end the speech segment
            silence_duration = current_time - self.last_speech_time
            
            if silence_duration >= self.max_silence_duration:
                # End of speech segment - process it
                await self._process_speech_segment(state)
                self.is_in_speech = False
    
    async def _check_pending_speech(self, state: ConnectionState) -> None:
        """Check if there's pending speech to process."""
        if self.is_in_speech and len(self.speech_buffer) > 0:
            current_time = time.time()
            silence_duration = current_time - self.last_speech_time
            
            if silence_duration >= self.max_silence_duration:
                await self._process_speech_segment(state)
                self.is_in_speech = False

    async def _process_speech_segment(self, state: ConnectionState) -> None:
        """Process a complete speech segment through the S2ST pipeline."""
        if len(self.speech_buffer) == 0:
            return
        
        # Check minimum duration
        duration = len(self.speech_buffer) / (settings.audio_sample_rate * 2)  # 2 bytes per sample
        if duration < self.min_speech_duration:
            logger.debug(f"Speech segment too short ({duration:.2f}s), skipping")
            self.speech_buffer.clear()
            return
        
        logger.info(f"Processing speech segment ({duration:.2f}s) for call {state.call_id}")
        state.is_processing = True

        # Generate segment ID and track timing
        segment_id = f"{state.call_id}_{int(time.time() * 1000)}"
        start_time = time.time()

        with self.create_span("s2st.process_speech_segment",
                             call_id=state.call_id,
                             segment_id=segment_id,
                             audio_duration_seconds=duration,
                             audio_size_bytes=len(self.speech_buffer)) as parent_span:
            try:
                # 1. Speech-to-Text
                speech_data = bytes(self.speech_buffer)

                with self.create_span("s2st.stt") as stt_span:
                    stt_start_time = time.time()
                    text, confidence = await self.stt.transcribe(speech_data)
                    stt_duration = time.time() - stt_start_time

                    stt_span.set_attribute("stt.confidence", confidence)
                    stt_span.set_attribute("stt.text_length", len(text))

                    # Record STT metrics
                    MetricsCollector.record_stt_request(
                        provider=settings.stt_provider,
                        status="success",
                        duration=stt_duration,
                        confidence=confidence
                    )

                if not text.strip():
                    logger.debug("No text transcribed, skipping segment")
                    parent_span.set_attribute("result", "no_text")
                    return

                logger.info(f"Transcribed: '{text}' (confidence: {confidence:.2f})")

                # 2. Translation
                with self.create_span("s2st.translation") as trans_span:
                    translation_start_time = time.time()
                    translated_text = await self.translator.translate(text)
                    translation_duration = time.time() - translation_start_time

                    trans_span.set_attribute("translation.source_length", len(text))
                    trans_span.set_attribute("translation.target_length", len(translated_text))

                    # Record translation metrics
                    MetricsCollector.record_translation_request(
                        provider=settings.translation_provider,
                        status="success",
                        duration=translation_duration
                    )

                logger.info(f"Translated: '{translated_text}'")

                # 3. Text-to-Speech
                with self.create_span("s2st.tts") as tts_span:
                    tts_start_time = time.time()
                    tts_audio = await self.tts.synthesize(translated_text)
                    tts_duration = time.time() - tts_start_time

                    if tts_audio:
                        tts_span.set_attribute("tts.audio_size_bytes", len(tts_audio))

                    # Record TTS metrics
                    MetricsCollector.record_tts_request(
                        provider=settings.tts_provider,
                        status="success" if tts_audio else "failed",
                        duration=tts_duration
                    )

                if tts_audio:
                    # Send translated audio to outbound queue
                    await state.outbound_queue.put(tts_audio)
                    state.total_audio_sent += len(tts_audio)
                    logger.info(f"TTS audio sent ({len(tts_audio)} bytes)")

                # 4. Add to transcript
                state.add_transcript_entry(text, translated_text, confidence)
                state.segments_processed += 1

                # Record segment processing metrics
                total_processing_duration = time.time() - start_time
                MetricsCollector.record_speech_segment_processed(
                    call_id=state.call_id,
                    provider=settings.stt_provider
                )
                MetricsCollector.record_processing_duration(
                    call_id=state.call_id,
                    stage="segment_total",
                    duration=total_processing_duration
                )

                parent_span.set_attribute("result", "success")
                # 5. Publish transcription event
                try:
                    event_publisher = await get_event_publisher()
                    processing_time_ms = int((time.time() - start_time) * 1000)
                    audio_duration_ms = int(duration * 1000)

                    providers_used = {
                        "stt": settings.stt_provider,
                        "translation": settings.translation_provider,
                        "tts": settings.tts_provider
                    }

                    await event_publisher.publish_transcription(
                        call_id=state.call_id,
                        segment_id=segment_id,
                        original_text=text,
                        translated_text=translated_text,
                        confidence_score=confidence,
                        processing_time_ms=processing_time_ms,
                        audio_duration_ms=audio_duration_ms,
                        providers_used=providers_used
                    )
                except Exception as e:
                    logger.warning(f"Failed to publish transcription event: {e}")

                parent_span.set_attribute("transcription.text", text[:100])  # First 100 chars
                parent_span.set_attribute("translation.text", translated_text[:100])  # First 100 chars

            except Exception as e:
                self.set_span_error(parent_span, e)
                logger.error(f"Error processing speech segment: {e}")
                state.add_error("segment_processing_error", str(e))

            finally:
                self.speech_buffer.clear()
                state.is_processing = False

    async def _prewarm_models(self) -> None:
        """Pre-warm all models with dummy data to eliminate first-call delays."""
        # Create dummy audio data (1 second of silence at 16kHz, 16-bit mono)
        sample_rate = settings.audio_sample_rate
        duration_seconds = 1.0
        num_samples = int(sample_rate * duration_seconds)

        # Generate dummy audio (silence with slight noise to avoid empty detection)
        import numpy as np
        dummy_audio = np.random.normal(0, 0.001, num_samples).astype(np.float32)
        dummy_audio_bytes = (dummy_audio * 32767).astype(np.int16).tobytes()

        logger.info("Pre-warming STT model...")
        try:
            await self.stt.transcribe(dummy_audio_bytes)
            logger.info("STT model pre-warmed successfully")
        except Exception as e:
            logger.warning(f"STT pre-warming failed: {e}")

        logger.info("Pre-warming translation model...")
        try:
            await self.translator.translate("Hello world")
            logger.info("Translation model pre-warmed successfully")
        except Exception as e:
            logger.warning(f"Translation pre-warming failed: {e}")

        logger.info("Pre-warming TTS model...")
        try:
            await self.tts.synthesize("Hello")
            logger.info("TTS model pre-warmed successfully")
        except Exception as e:
            logger.warning(f"TTS pre-warming failed: {e}")

        logger.info("Model pre-warming completed")
